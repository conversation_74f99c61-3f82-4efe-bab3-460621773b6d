import sys, requests, uuid

if len(sys.argv) != 3:
    print("Usage: python get_master_token.py <gmail> <app_password>")
    sys.exit(1)

email, app_pwd = sys.argv[1:3]

payload = {
    "Email": "<EMAIL>",
    "Passwd": "bppjpvwspocrwhqs",
    "accountType": "HOSTED_OR_GOOGLE",
    "has_permission": "1",
    "source": "android",
    "device_country": "us",
    "lang": "en",
    "sdk_version": "28",
    "service": "ac2dm"            # this returns the master token
}

r = requests.post("https://android.clients.google.com/auth", data=payload)
if r.status_code != 200:
    print("HTTP", r.status_code, r.text)
    sys.exit(1)

for line in r.text.splitlines():
    if line.startswith("Auth="):
        print("\nMaster token (aas_et…):\n" + line[5:])
        break
else:
    print("Login worked but no Auth= line — check credentials/app-password")
